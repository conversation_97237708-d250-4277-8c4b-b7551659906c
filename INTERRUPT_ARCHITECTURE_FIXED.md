# Fixed Interrupt System Architecture

## Overview
The interrupt system has been restructured to respect proper architectural hierarchy and eliminate circular dependencies. The system now follows a clear responsibility chain where lower-level components (TTSState) cannot directly call higher-level orchestrators (StateManager).

## Architectural Hierarchy Problem (FIXED)

### ❌ Previous Broken Architecture:
```
TTSState → StateManager.handle_interrupt_event() [CIRCULAR DEPENDENCY]
```

### ✅ Fixed Architecture:
```
TTSState → Memory Storage → StateManager Monitoring → Interrupt Handling
```

## Correct Responsibility Chain

### 1. **TTSState** (Lower Level - Interrupt Detection)
**Responsibility**: Detect interrupts, pause TTS, store context in memory
**Does NOT**: Call StateManager directly

```python
# TTSState._handle_interrupt_detected()
async def _handle_interrupt_detected(self, interrupt_data, context):
    # Extract context information
    explicit_reversibility = context.get("explicit_reversibility", True)
    
    # Store interrupt context in memory (NOT call StateManager)
    await memory_manager.set_interrupt_context(
        detected=True,
        confirmed=True,
        user_input_queued=interrupt_data.get("user_input"),
        resume_after_acknowledgment=explicit_reversibility,
        action_reversible=explicit_reversibility
    )
    
    # Log interrupt event for StateManager to find
    await memory_manager.add_interrupt_event(
        event_type="interruption_detected",
        details=complete_interrupt_data
    )
```

### 2. **StateManager** (Higher Level - Interrupt Orchestration)
**Responsibility**: Monitor memory for interrupts, orchestrate interrupt handling flow
**Does**: Proactively check for interrupt context after each state execution

```python
# StateManager._monitor_and_handle_interrupts()
async def _monitor_and_handle_interrupts(self):
    # Check memory for interrupt context
    interrupt_context = await self.memory_manager.get_interrupt_context()
    
    if interrupt_context.get("detected") and not interrupt_context.get("handled"):
        # Get complete interrupt data from event history
        interrupt_history = await self.memory_manager.get_interrupt_history()
        latest_interrupt = find_latest_interrupt_event(interrupt_history)
        
        # Mark as being handled to prevent duplicates
        interrupt_context["handled"] = True
        await self.memory_manager.set_interrupt_context(**interrupt_context)
        
        # Handle the interrupt using complete flow
        await self.handle_interrupt_event(latest_interrupt)
```

### 3. **Orchestrator** (Highest Level - Coordination)
**Responsibility**: High-level monitoring and coordination
**Does**: Monitor interrupt status, coordinate with other high-level components

```python
# OrchestratorAgentV2 - Simplified monitoring
interrupt_context = await memory_manager.get_interrupt_context()
if interrupt_context.get("detected") and not interrupt_context.get("handled"):
    self.logger.info("Interrupt detected. StateManager monitoring will handle the flow.")
    # No direct intervention - StateManager handles it
```

## Complete Flow Diagram

```
1. USER INTERRUPTS DURING TTS
         ↓
2. VAD detects speech → TTSPlaybackController pauses audio
         ↓
3. TTSState._handle_interrupt_detected()
   - Extracts context (reversibility, side effects)
   - Stores interrupt context in memory
   - Logs interrupt event
   - NO direct StateManager call
         ↓
4. StateManager.execute_step() completes
         ↓
5. StateManager._monitor_and_handle_interrupts()
   - Checks memory for interrupt context
   - Finds unhandled interrupt
   - Marks as "handled" to prevent duplicates
         ↓
6. StateManager.handle_interrupt_event()
         ↓
7. StateManager._transition_to_interrupt_handler()
   - Creates InterruptHandlerState
   - Executes with proper context
         ↓
8. InterruptHandlerState.process()
   - Determines reversibility
   - Generates acknowledgment
         ↓
9. StateManager.resume_after_interrupt()
   - Speaks acknowledgment
   - Handles reversibility-based flow
   - Cleans up context
```

## Key Architectural Fixes

### 1. **Eliminated Circular Dependency**
- TTSState no longer calls StateManager directly
- Communication happens through memory storage
- StateManager monitors memory proactively

### 2. **Proper Separation of Concerns**
- **TTSState**: Interrupt detection and context storage
- **StateManager**: Interrupt orchestration and flow management
- **Orchestrator**: High-level coordination and monitoring

### 3. **Memory-Based Communication**
- TTSState stores interrupt context in memory
- StateManager monitors memory after each execution
- Clean separation between detection and handling

### 4. **Duplicate Prevention**
- Added `handled` flag to interrupt context
- StateManager marks interrupts as handled
- Prevents duplicate processing

## Real-Life Flow Examples

### Example 1: Reversible Action (Weather Query)
```
1. User: "What's the weather today?"
2. TTSState starts: "The weather today is sunny..."
3. User interrupts: "What about tomorrow?"
4. TTSState detects interrupt → stores in memory
5. StateManager monitors → finds interrupt context
6. StateManager → InterruptHandlerState (reversible=True)
7. AI: "I understand, let me help you with that instead."
8. StateManager processes new input immediately
9. AI: "Tomorrow's weather will be cloudy..."
```

### Example 2: Irreversible Action (Money Transfer)
```
1. User: "Transfer $500 to John"
2. TTSState starts: "Processing transfer of $500..."
3. User interrupts: "Wait, stop! I meant $50!"
4. TTSState detects interrupt → stores in memory
5. StateManager monitors → finds interrupt context
6. StateManager → InterruptHandlerState (reversible=False)
7. AI: "I understand you want to say something. Let me finish this important information first."
8. StateManager resumes original TTS
9. AI: "...Transfer completed. Transaction ID: TXN123456."
10. StateManager processes queued input
11. AI: "I see you mentioned $50. Unfortunately, the transfer has been processed..."
```

## Memory Context Structure

### Interrupt Context in Memory:
```python
{
    "detected": True,
    "confirmed": True,
    "handled": False,  # NEW: Prevents duplicate processing
    "user_input_queued": "What about tomorrow?",
    "resume_after_acknowledgment": True,
    "action_reversible": True,
    "interrupt_timestamp": "2025-01-14T10:30:45Z",
    "last_updated": "2025-01-14T10:30:45Z"
}
```

### Interrupt Event in History:
```python
{
    "event_type": "interruption_detected",
    "timestamp": "2025-01-14T10:30:45Z",
    "session_id": "session_123",
    "details": {
        "user_input": "What about tomorrow?",
        "playback_position": 3.2,
        "audio_path": "/tmp/weather.mp3",
        "explicit_reversibility": True,
        "explicit_side_effect": "none",
        "explicit_post_tts_policy": "allow_interrupt"
    }
}
```

## Benefits of Fixed Architecture

### 1. **No Circular Dependencies**
- Clean separation between layers
- TTSState doesn't know about StateManager
- Proper hierarchical design

### 2. **Scalable Design**
- Easy to add new interrupt sources
- StateManager can monitor multiple components
- Centralized interrupt handling

### 3. **Testable Components**
- TTSState can be tested independently
- StateManager interrupt handling is isolated
- Clear interfaces between components

### 4. **Maintainable Code**
- Single responsibility principle
- Clear data flow
- Predictable behavior

## Files Modified

1. **`core/state_manager/state_output.py`**:
   - Removed direct StateManager calls
   - Added memory-based interrupt context storage
   - Clean separation of concerns

2. **`core/state_manager/state_manager.py`**:
   - Added `_monitor_and_handle_interrupts()` method
   - Proactive interrupt monitoring after state execution
   - Added `handled` flag support

3. **`agents/orchestration/orchestrator_agent_v2.py`**:
   - Simplified to monitoring only
   - Removed direct interrupt handling
   - Clean high-level coordination

4. **`core/memory/memory_manager.py`**:
   - Added `handled` flag support
   - Enhanced interrupt context structure

5. **`test_interrupt_flow.py`**:
   - Updated to reflect new architecture
   - Tests memory-based communication
   - Validates proper separation

## Validation

The fixed architecture ensures:
- ✅ No circular dependencies
- ✅ Proper hierarchical design
- ✅ Clean separation of concerns
- ✅ Scalable and maintainable code
- ✅ Testable components
- ✅ Predictable interrupt handling

The interrupt system now follows proper software architecture principles while maintaining full functionality for both reversible and irreversible actions.
