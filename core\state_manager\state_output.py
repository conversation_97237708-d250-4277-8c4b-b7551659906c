from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Type
from pydantic import ValidationError
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.a2a_message import A2AMessage, MessageType
from core.logging.logger_config import get_module_logger
import asyncio
from schemas.layer2_schema import (
    STTInputSchema, STTOutputSchema,
    PreProcessingInputSchema, PreProcessingOutputSchema,
    ProcessingInputSchema, ProcessingOutputSchema,
    FillerInputSchema, FillerOutputSchema,
    TTSInputSchema, TTSOutputSchema
)

"""
Pipeline States v2
------------------
This module defines the abstract base class and concrete pipeline state classes for the new state-based pipeline management system.
Each state retrieves its agent from the agent registry, uses the agent's process method, and handles messaging, validation, and error handling.

Enhanced with proper Pydantic schema validation for input/output data.
"""


class AbstractPipelineState(ABC):
    """
    Abstract base class for all pipeline states.

    Provides schema validation, agent orchestration, and notification publishing.
    Each concrete state must define input_schema_class and output_schema_class.
    """
    id: str
    input_schema_class: Type = None
    output_schema_class: Type = None

    def __init__(self, state_id: str, agent_registry: Any, session_id: str):
        self.id = state_id
        self.agent_registry = agent_registry
        self.session_id = session_id
        self.logger = get_module_logger("pipeline_state", session_id=self.session_id, state_id=self.id)

    @abstractmethod
    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        pass

    # @classmethod
    # def get_input_schema(cls) -> Dict[str, Any]:
    #     """Get input schema as dictionary for compatibility"""
    #     if cls.input_schema_class:
    #         return cls.input_schema_class.model_json_schema()
    #     return {}

    # @classmethod
    # def get_output_schema(cls) -> Dict[str, Any]:
    #     """Get output schema as dictionary for compatibility"""
    #     if cls.output_schema_class:
    #         return cls.output_schema_class.model_json_schema()
    #     return {}

    def validate_input(self, input_data: Dict[str, Any]) -> Any:
        if not self.input_schema_class:
            self.logger.warning(f"No input schema defined for state {self.id}")
            return input_data
        try:
            validated_input = self.input_schema_class(**input_data)
            self.logger.debug(f"Input validation successful for state {self.id}")
            return validated_input
        except ValidationError as e:
            self.logger.error(f"Input validation failed for state {self.id}: {e}")
            raise

    def validate_output(self, output_data: Dict[str, Any]) -> Any:
        if not self.output_schema_class:
            self.logger.warning(f"No output schema defined for state {self.id}")
            return output_data
        try:
            validated_output = self.output_schema_class(**output_data)
            self.logger.debug(f"Output validation successful for state {self.id}")
            return validated_output
        except ValidationError as e:
            self.logger.error(f"Output validation failed for state {self.id}: {e}")
            raise

    async def _publish_notification(self, status: str, payload: Dict[str, Any], context_keys_updated=None, error_message=None):
        notification = A2AMessage(
            session_id=self.session_id,
            message_type=MessageType.NOTIFICATION,
            source_agent=self.id,
            target_agent="Orchestrator",
            payload={"status": status, **({"error_message": error_message} if error_message else {}), **payload},
            context_keys_updated=context_keys_updated or []
        )
        redis_client = self.agent_registry._redis
        await redis_client.publish("agent_completion", notification.to_json())


# --- Concrete Pipeline States ---

class STTState(AbstractPipelineState):
    """
    Speech-to-Text State
    Converts audio input to text transcript with latency tracking.
    """
    input_schema_class = STTInputSchema
    output_schema_class = STTOutputSchema

    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        try:
            validated_input = self.validate_input(input_data)
        except ValidationError as e:
            return StateOutput(
                status=StatusType.ERROR,
                message=f"STTState input validation error: {str(e)}",
                code=StatusCode.VALIDATION_ERROR,
                outputs={},
                meta={"agent": self.id, "validation_error": str(e)}
            )
        agent = self.agent_registry.getAgent("stt_agent")
        try:
            result = await agent.process(validated_input.model_dump(), context)
            try:
                validated_output = self.validate_output(result.outputs)
                result.outputs = validated_output.model_dump()
            except ValidationError as e:
                self.logger.warning(f"STTState output validation failed: {e}")
            await self._publish_notification("complete", {"latencySTT": result.outputs.get("latencySTT")}, ["transcript", "latencySTT"])
            return result
        except Exception as e:
            await self._publish_notification("error", {}, error_message=str(e))
            return StateOutput(
                status=StatusType.ERROR,
                message=f"STTState error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"agent": self.id, "error": str(e)}
            )

class PreProcessingState(AbstractPipelineState):
    """
    Preprocessing State
    Cleans text and extracts intent, emotion, and gender information.
    """
    input_schema_class = PreProcessingInputSchema
    output_schema_class = PreProcessingOutputSchema

    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        try:
            validated_input = self.validate_input(input_data)
        except ValidationError as e:
            return StateOutput(
                status=StatusType.ERROR,
                message=f"PreProcessingState input validation error: {str(e)}",
                code=StatusCode.VALIDATION_ERROR,
                outputs={},
                meta={"agent": self.id, "validation_error": str(e)}
            )
        agent = self.agent_registry.getAgent("preprocessing_agent")
        try:
            result = await agent.process(validated_input.model_dump(), context)
            try:
                validated_output = self.validate_output(result.outputs)
                result.outputs = validated_output.model_dump()
            except ValidationError as e:
                self.logger.warning(f"PreProcessingState output validation failed: {e}")
            await self._publish_notification("complete", {"latencyPreprocessing": result.outputs.get("latencyPreprocessing")}, ["clean_text", "intent", "emotion", "gender", "latencyPreprocessing"])
            return result
        except Exception as e:
            await self._publish_notification("error", {}, error_message=str(e))
            return StateOutput(
                status=StatusType.ERROR,
                message=f"PreProcessingState error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"agent": self.id, "error": str(e)}
            )

class ProcessingState(AbstractPipelineState):
    """
    Processing State
    Processes clean text and intent to generate LLM responses and business logic results.
    """
    input_schema_class = ProcessingInputSchema
    output_schema_class = ProcessingOutputSchema

    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        try:
            validated_input = self.validate_input(input_data)
        except ValidationError as e:
            return StateOutput(
                status=StatusType.ERROR,
                message=f"ProcessingState input validation error: {str(e)}",
                code=StatusCode.VALIDATION_ERROR,
                outputs={},
                meta={"agent": self.id, "validation_error": str(e)}
            )
        agent = self.agent_registry.getAgent("processing_agent")
        try:
            result = await agent.process(validated_input.model_dump(), context)
            try:
                validated_output = self.validate_output(result.outputs)
                result.outputs = validated_output.model_dump()
            except ValidationError as e:
                self.logger.warning(f"ProcessingState output validation failed: {e}")
            await self._publish_notification("complete", {"latencyProcessing": result.outputs.get("latencyProcessing")}, ["llm_answer", "account_balance", "loan_eligibility", "exit_signal", "latencyProcessing"])
            return result
        except Exception as e:
            await self._publish_notification("error", {}, error_message=str(e))
            return StateOutput(
                status=StatusType.ERROR,
                message=f"ProcessingState error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"agent": self.id, "error": str(e)}
            )

class FillerState(AbstractPipelineState):
    """
    Filler State
    Generates filler audio content for conversation flow management.
    """
    input_schema_class = FillerInputSchema
    output_schema_class = FillerOutputSchema

    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        try:
            validated_input = self.validate_input(input_data)
        except ValidationError as e:
            return StateOutput(
                status=StatusType.ERROR,
                message=f"FillerState input validation error: {str(e)}",
                code=StatusCode.VALIDATION_ERROR,
                outputs={},
                meta={"agent": self.id, "validation_error": str(e)}
            )
        agent = self.agent_registry.getAgent("filler_tts_agent")
        try:
            result = await agent.process(validated_input.model_dump(), context)
            try:
                validated_output = self.validate_output(result.outputs)
                result.outputs = validated_output.model_dump()
            except ValidationError as e:
                self.logger.warning(f"FillerState output validation failed: {e}")
            await self._publish_notification("filler", {"audio_path": result.outputs.get("audio_path"), "filler_text": result.outputs.get("filler_text")}, ["audio_path", "filler_text"])
            return result
        except Exception as e:
            await self._publish_notification("error", {}, error_message=str(e))
            return StateOutput(
                status=StatusType.ERROR,
                message=f"FillerState error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"agent": self.id, "error": str(e)}
            )

class TTSState(AbstractPipelineState):
    """
    Text-to-Speech State with Interrupt Handling
    Converts text to speech with emotion and gender parameters.
    Supports interrupt detection during playback.
    """
    input_schema_class = TTSInputSchema
    output_schema_class = TTSOutputSchema

    def __init__(self, state_id: str, agent_registry, session_id: str, interrupt_config=None):
        super().__init__(state_id, agent_registry, session_id)
        self.playback_controller = None
        self.interrupt_enabled = True
        self.interrupt_config = interrupt_config

    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        try:
            validated_input = self.validate_input(input_data)
        except ValidationError as e:
            return StateOutput(
                status=StatusType.ERROR,
                message=f"TTSState input validation error: {str(e)}",
                code=StatusCode.VALIDATION_ERROR,
                outputs={},
                meta={"agent": self.id, "validation_error": str(e)}
            )

        agent = self.agent_registry.getAgent("tts_agent")
        try:
            # Generate TTS audio
            result = await agent.process(validated_input.model_dump(), context)
            try:
                validated_output = self.validate_output(result.outputs)
                result.outputs = validated_output.model_dump()
            except ValidationError as e:
                self.logger.warning(f"TTSState output validation failed: {e}")

            # === ENHANCED: Extract explicit reversibility/side effect/policy from context ===
            explicit_reversibility = None
            explicit_side_effect = None
            explicit_post_tts_policy = None
            if context:
                # Try to get from workflow state config (if present)
                state_config = context.get("workflow_state_config") or context.get("state_config")
                if state_config:
                    explicit_reversibility = state_config.get("reversible")
                    explicit_side_effect = state_config.get("has_side_effect")
                    explicit_post_tts_policy = state_config.get("post_tts_policy")
            # Pass these into the interrupt handler via context
            if context is not None:
                context["explicit_reversibility"] = explicit_reversibility
                context["explicit_side_effect"] = explicit_side_effect
                context["explicit_post_tts_policy"] = explicit_post_tts_policy
            # === END ENHANCED ===

            # If interrupt handling is enabled and we have an audio path, start playback with interrupt detection
            audio_path = result.outputs.get("audio_path")
            if self.interrupt_enabled and audio_path:
                interrupt_result = await self._handle_tts_with_interrupts(audio_path, context)
                if interrupt_result.get("interrupted"):
                    # Merge interrupt information into the result
                    result.outputs.update(interrupt_result)
                    result.message = "TTS completed with interrupt handling"

            await self._publish_notification("complete", {"latencyTTS": result.outputs.get("latencyTTS")}, ["audio_path", "latencyTTS"])
            return result

        except Exception as e:
            await self._publish_notification("error", {}, error_message=str(e))
            return StateOutput(
                status=StatusType.ERROR,
                message=f"TTSState error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"agent": self.id, "error": str(e)}
            )

    async def _handle_tts_with_interrupts(self, audio_path: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Handle TTS playback with interrupt detection and partial resume support.

        Args:
            audio_path: Path to the generated audio file
            context: Session context

        Returns:
            Dict with interrupt handling results
        """
        if self.interrupt_config and not self.interrupt_config.detection.enabled:
            self.logger.info(
                "Interrupt detection is disabled in config.",
                action="_handle_tts_with_interrupts",
                layer="tts_state"
            )
            return {"interrupted": False, "interrupt_detection_disabled": True}
        try:
            from utils.audio_utils import TTSPlaybackController

            # Initialize playback controller
            self.playback_controller = TTSPlaybackController(self.session_id, interrupt_config=self.interrupt_config)

            # Set up interrupt callback
            async def interrupt_callback(interrupt_data):
                await self._handle_interrupt_detected(interrupt_data, context)

            # --- PARTIAL RESUME LOGIC ---
            memory_manager = getattr(self, 'memory_manager', None)
            if memory_manager is None and context is not None:
                memory_manager = context.get('memory_manager')
            if memory_manager is None and hasattr(self.agent_registry, 'memory_manager'):
                memory_manager = self.agent_registry.memory_manager
            resume_from_position = 0.0
            if memory_manager:
                tts_playback_state = await memory_manager.get_tts_playback_state()
                if tts_playback_state and tts_playback_state.get("audio_path") == audio_path:
                    resume_from_position = tts_playback_state.get("playback_position", 0.0)

            # Start playback with interrupt detection and partial resume
            playback_result = await self.playback_controller.start_playback_with_interrupt_detection(
                audio_path, interrupt_callback, resume_from_position=resume_from_position
            )

            if playback_result.status == StatusType.SUCCESS:
                # Monitor playback for completion or interruption
                return await self._monitor_playback()
            else:
                self.logger.warning(
                    "Failed to start TTS playback with interrupt detection",
                    action="_handle_tts_with_interrupts",
                    reason=playback_result.message,
                    layer="tts_state"
                )
                return {"interrupted": False, "playback_error": playback_result.message}

        except Exception as e:
            self.logger.error(
                "Error in TTS interrupt handling",
                action="_handle_tts_with_interrupts",
                reason=str(e),
                layer="tts_state"
            )
            return {"interrupted": False, "error": str(e)}

    async def _monitor_playback(self) -> Dict[str, Any]:
        """Monitor playback for completion or interruption."""
        try:
            # Simple monitoring loop
            while self.playback_controller.is_playing:
                await asyncio.sleep(0.1)

                if self.playback_controller.interrupt_detected:
                    return {
                        "interrupted": True,
                        "playback_position": self.playback_controller.get_current_playback_position(),
                        "interrupt_timestamp": self.playback_controller.get_playback_status().get("interrupt_timestamp")
                    }

            # Playback completed normally
            return {
                "interrupted": False,
                "playback_completed": True,
                "total_duration": self.playback_controller.audio_duration
            }

        except Exception as e:
            self.logger.error(
                "Error monitoring playback",
                action="_monitor_playback",
                reason=str(e),
                layer="tts_state"
            )
            return {"interrupted": False, "monitoring_error": str(e)}

    async def _handle_interrupt_detected(self, interrupt_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None):
        """Handle when an interrupt is detected during TTS playback."""
        try:
            self.logger.info(
                "Interrupt detected during TTS playback",
                action="_handle_interrupt_detected",
                input_data=interrupt_data,
                layer="tts_state"
            )

            # Get memory manager (from context or agent_registry)
            memory_manager = getattr(self, 'memory_manager', None)
            if memory_manager is None and context is not None:
                memory_manager = context.get('memory_manager')
            if memory_manager is None and hasattr(self.agent_registry, 'memory_manager'):
                memory_manager = self.agent_registry.memory_manager

            # Extract reversibility information from context
            explicit_reversibility = context.get("explicit_reversibility", True) if context else True
            explicit_side_effect = context.get("explicit_side_effect", "none") if context else "none"
            explicit_post_tts_policy = context.get("explicit_post_tts_policy", "allow_interrupt") if context else "allow_interrupt"

            self.logger.info(
                "Interrupt context explicit fields",
                action="_handle_interrupt_detected",
                output_data={
                    "explicit_reversibility": explicit_reversibility,
                    "explicit_side_effect": explicit_side_effect,
                    "explicit_post_tts_policy": explicit_post_tts_policy
                },
                layer="tts_state"
            )

            # Prepare complete interrupt data with context
            complete_interrupt_data = {
                **interrupt_data,
                "explicit_reversibility": explicit_reversibility,
                "explicit_side_effect": explicit_side_effect,
                "explicit_post_tts_policy": explicit_post_tts_policy,
                "confirmed": True,
                "requires_handling": True,
                "resume_after_acknowledgment": explicit_reversibility,
                "action_reversible": explicit_reversibility
            }

            # Get StateManager from agent_registry and trigger interrupt handling
            state_manager = getattr(self.agent_registry, 'state_manager', None)
            if state_manager is not None:
                self.logger.info(
                    "Notifying StateManager of interrupt event",
                    action="_handle_interrupt_detected",
                    output_data={"interrupt_data": complete_interrupt_data},
                    layer="tts_state"
                )
                await state_manager.handle_interrupt_event(complete_interrupt_data)
            else:
                # Fallback: store in memory directly if StateManager not available
                if memory_manager:
                    await memory_manager.set_interrupted_flag(True)
                    # Store TTS playback state with playback position and message hash
                    import hashlib
                    audio_path = interrupt_data.get("audio_path")
                    playback_position = interrupt_data.get("playback_position")
                    message_hash = None
                    if audio_path:
                        message_hash = hashlib.sha256(audio_path.encode()).hexdigest()
                    await memory_manager.set_tts_playback_state(
                        audio_path=audio_path,
                        status="interrupted",
                        playback_position=playback_position,
                        message_hash=message_hash
                    )
                    # Log interruption event
                    await memory_manager.add_interrupt_event(
                        event_type="interruption_detected",
                        details=complete_interrupt_data
                    )

                self.logger.warning(
                    "StateManager not available, stored interrupt context in memory only",
                    action="_handle_interrupt_detected",
                    layer="tts_state"
                )

        except Exception as e:
            self.logger.error(
                "Error handling interrupt detection",
                action="_handle_interrupt_detected",
                reason=str(e),
                layer="tts_state"
            )