#!/usr/bin/env python3
"""
Test script to validate the complete interrupt handling flow.
Tests both reversible and irreversible action scenarios.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.state_manager.state_manager import StateManager
from core.state_manager.state_output import TTSState
from core.interruption.interrupt_handler_state import InterruptHandlerState
from core.interruption.action_reversibility import ActionReversibilityDetector
from core.memory.memory_manager import MemoryManager
from core.config.interrupt_config import InterruptConfig, InterruptDetectionConfig
from utils.audio_utils import TTSPlaybackController


class MockRedisClient:
    """Mock Redis client for testing."""

    async def publish(self, channel: str, message: str):
        """Mock publish method."""
        print(f"[MOCK REDIS] Published to {channel}: {message}")


class MockAgentRegistry:
    """Mock agent registry for testing."""

    def __init__(self):
        self.agents = {}
        self._redis = MockRedisClient()  # Mock Redis client
        self.memory_manager = None  # Will be set later

    def register(self, name: str, agent):
        """Register an agent."""
        self.agents[name] = agent

    def getAgent(self, name: str):
        """Get an agent by name."""
        return self.agents.get(name)


class MockTTSAgent:
    """Mock TTS agent for testing."""

    def __init__(self):
        self.agent_name = "tts_agent"

    async def process(self, input_data: dict, context: dict = None):
        """Mock TTS agent process method."""
        from schemas.outputSchema import StateOutput, StatusType, StatusCode

        # Simulate TTS processing
        await asyncio.sleep(0.1)

        text = input_data.get("text", "")
        return StateOutput(
            status=StatusType.SUCCESS,
            message="TTS synthesis completed",
            code=StatusCode.OK,
            outputs={
                "audio_path": f"/tmp/mock_tts_{hash(text)}.mp3",
                "latencyTTS": 0.1
            }
        )

    async def text_to_speech(self, text: str, voice_config: dict = None):
        """Mock TTS synthesis."""
        from schemas.outputSchema import StateOutput, StatusType, StatusCode

        # Simulate TTS processing
        await asyncio.sleep(0.1)

        return StateOutput(
            status=StatusType.SUCCESS,
            message="TTS synthesis completed",
            code=StatusCode.OK,
            outputs={
                "audio_path": f"/tmp/mock_tts_{hash(text)}.mp3",
                "latencyTTS": 0.1
            }
        )


async def test_reversible_interrupt():
    """Test interrupt handling for reversible actions (weather query)."""
    print("\n=== Testing Reversible Action Interrupt ===")

    # Setup
    session_id = "test_session_reversible"
    memory_manager = MemoryManager(session_id, "test_user")
    agent_registry = MockAgentRegistry()
    agent_registry.register("tts_agent", MockTTSAgent())
    agent_registry.memory_manager = memory_manager  # Add memory manager to registry

    interrupt_config = InterruptConfig(
        detection=InterruptDetectionConfig(
            enabled=True,
            vad_threshold=0.01,
            confirmation_window_seconds=0.5
        )
    )

    # Create TTS State
    tts_state = TTSState(
        state_id="tts_state",
        agent_registry=agent_registry,
        session_id=session_id,
        interrupt_config=interrupt_config
    )

    # Test context for reversible action (weather query)
    context = {
        "explicit_reversibility": True,
        "explicit_side_effect": "none",
        "explicit_post_tts_policy": "allow_interrupt"
    }

    # Simulate TTS processing with required fields
    input_data = {
        "text": "The weather today is sunny with a temperature of 75 degrees...",
        "emotion": "neutral",
        "gender": "female"
    }

    print("1. Starting TTS for weather query...")
    result = await tts_state.process(input_data, context)
    print(f"   TTS Result: {result.status.value} - {result.message}")

    # Simulate interrupt during TTS (TTSState stores context in memory)
    print("2. Simulating user interrupt: 'What about tomorrow?'")
    interrupt_data = {
        "session_id": session_id,
        "user_input": "What about tomorrow?",
        "playback_position": 3.2,
        "audio_path": result.outputs.get("audio_path"),
        "timestamp": "2025-01-14T10:30:45Z"
    }

    # Simulate TTSState detecting interrupt and storing in memory
    await tts_state._handle_interrupt_detected(interrupt_data, context)
    print("   TTSState stored interrupt context in memory")

    # Check interrupt context was stored
    interrupt_context = await memory_manager.get_interrupt_context()
    print(f"3. Interrupt context stored: {interrupt_context.get('detected', False)}")
    print(f"   Action reversible: {interrupt_context.get('action_reversible', False)}")
    print(f"   User input queued: {interrupt_context.get('user_input_queued')}")

    print("✅ Reversible interrupt test completed")


async def test_irreversible_interrupt():
    """Test interrupt handling for irreversible actions (money transfer)."""
    print("\n=== Testing Irreversible Action Interrupt ===")

    # Setup
    session_id = "test_session_irreversible"
    memory_manager = MemoryManager(session_id, "test_user")
    agent_registry = MockAgentRegistry()
    agent_registry.register("tts_agent", MockTTSAgent())
    agent_registry.memory_manager = memory_manager  # Add memory manager to registry

    interrupt_config = InterruptConfig(
        detection=InterruptDetectionConfig(
            enabled=True,
            vad_threshold=0.01,
            confirmation_window_seconds=0.5
        )
    )

    # Create TTS State
    tts_state = TTSState(
        state_id="tts_state",
        agent_registry=agent_registry,
        session_id=session_id,
        interrupt_config=interrupt_config
    )

    # Test context for irreversible action (money transfer)
    context = {
        "explicit_reversibility": False,
        "explicit_side_effect": "high",
        "explicit_post_tts_policy": "no_interrupt"
    }

    # Simulate TTS processing with required fields
    input_data = {
        "text": "I'm processing your transfer of $500 to John Smith's account...",
        "emotion": "neutral",
        "gender": "female"
    }

    print("1. Starting TTS for money transfer...")
    result = await tts_state.process(input_data, context)
    print(f"   TTS Result: {result.status.value} - {result.message}")

    # Simulate interrupt during TTS (TTSState stores context in memory)
    print("2. Simulating user interrupt: 'Wait, stop! I meant $50!'")
    interrupt_data = {
        "session_id": session_id,
        "user_input": "Wait, stop! I meant $50!",
        "playback_position": 4.1,
        "audio_path": result.outputs.get("audio_path"),
        "timestamp": "2025-01-14T10:35:22Z"
    }

    # Simulate TTSState detecting interrupt and storing in memory
    await tts_state._handle_interrupt_detected(interrupt_data, context)
    print("   TTSState stored interrupt context in memory")

    # Check interrupt context was stored
    interrupt_context = await memory_manager.get_interrupt_context()
    print(f"3. Interrupt context stored: {interrupt_context.get('detected', False)}")
    print(f"   Action reversible: {interrupt_context.get('action_reversible', False)}")
    print(f"   User input queued: {interrupt_context.get('user_input_queued')}")

    print("✅ Irreversible interrupt test completed")


async def test_interrupt_handler_state_directly():
    """Test InterruptHandlerState directly."""
    print("\n=== Testing InterruptHandlerState Directly ===")
    
    # Setup
    session_id = "test_session_direct"
    agent_registry = MockAgentRegistry()
    
    interrupt_config = InterruptConfig(
        detection=InterruptDetectionConfig(
            enabled=True,
            vad_threshold=0.01,
            confirmation_window_seconds=0.5
        )
    )
    
    # Create InterruptHandlerState
    interrupt_state = InterruptHandlerState(
        state_id="interrupt_handler",
        agent_registry=agent_registry,
        session_id=session_id,
        interrupt_config=interrupt_config
    )
    
    # Test reversible action
    print("1. Testing reversible action handling...")
    reversible_input = {
        "interrupt_detected": True,
        "user_input": "What about tomorrow?",
        "playback_position": 3.2,
        "audio_path": "/tmp/weather.mp3",
        "session_id": session_id
    }
    
    reversible_context = {
        "explicit_reversibility": True,
        "explicit_side_effect": "none",
        "explicit_post_tts_policy": "allow_interrupt"
    }
    
    result = await interrupt_state.process(reversible_input, reversible_context)
    print(f"   Reversible result: {result.outputs}")
    
    # Test irreversible action
    print("2. Testing irreversible action handling...")
    irreversible_input = {
        "interrupt_detected": True,
        "user_input": "Wait, stop!",
        "playback_position": 4.1,
        "audio_path": "/tmp/transfer.mp3",
        "session_id": session_id
    }
    
    irreversible_context = {
        "explicit_reversibility": False,
        "explicit_side_effect": "high",
        "explicit_post_tts_policy": "no_interrupt"
    }
    
    result = await interrupt_state.process(irreversible_input, irreversible_context)
    print(f"   Irreversible result: {result.outputs}")
    
    print("✅ InterruptHandlerState direct test completed")


async def main():
    """Run all interrupt flow tests."""
    print("🚀 Starting Interrupt Flow Integration Tests")
    
    try:
        await test_reversible_interrupt()
        await test_irreversible_interrupt()
        await test_interrupt_handler_state_directly()
        
        print("\n🎉 All interrupt flow tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
