#!/usr/bin/env python3
"""
Test script to validate the complete interrupt handling flow.
Tests both reversible and irreversible action scenarios.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.state_manager.state_manager import StateManager
from core.state_manager.state_output import TTSState
from core.interruption.interrupt_handler_state import InterruptHandlerState
from core.interruption.action_reversibility import ActionReversibilityDetector
from core.memory.memory_manager import MemoryManager
from core.config.interrupt_config import InterruptConfig, InterruptDetectionConfig
from agents.registry.agent_registry import AgentRegistry
from utils.audio_utils import TTSPlaybackController


class MockTTSAgent:
    """Mock TTS agent for testing."""
    
    def __init__(self):
        self.agent_name = "tts_agent"
    
    async def text_to_speech(self, text: str, voice_config: dict = None):
        """Mock TTS synthesis."""
        from schemas.outputSchema import StateOutput, StatusType, StatusCode
        
        # Simulate TTS processing
        await asyncio.sleep(0.1)
        
        return StateOutput(
            status=StatusType.SUCCESS,
            message="TTS synthesis completed",
            code=StatusCode.OK,
            outputs={
                "audio_path": f"/tmp/mock_tts_{hash(text)}.mp3",
                "latencyTTS": 0.1
            }
        )


async def test_reversible_interrupt():
    """Test interrupt handling for reversible actions (weather query)."""
    print("\n=== Testing Reversible Action Interrupt ===")
    
    # Setup
    session_id = "test_session_reversible"
    memory_manager = MemoryManager(session_id, "test_user")
    agent_registry = AgentRegistry()
    agent_registry.register("tts_agent", MockTTSAgent())
    
    interrupt_config = InterruptConfig(
        detection=InterruptDetectionConfig(
            enabled=True,
            vad_threshold=0.01,
            confirmation_window_seconds=0.5
        )
    )
    
    state_manager = StateManager(
        session_id=session_id,
        user_id="test_user",
        memory_manager=memory_manager,
        agent_registry=agent_registry,
        interrupt_config=interrupt_config
    )
    
    # Add state_manager to agent_registry for TTSState access
    agent_registry.state_manager = state_manager
    
    # Create TTS State
    tts_state = TTSState(
        state_id="tts_state",
        agent_registry=agent_registry,
        session_id=session_id,
        interrupt_config=interrupt_config
    )
    
    # Test context for reversible action (weather query)
    context = {
        "explicit_reversibility": True,
        "explicit_side_effect": "none",
        "explicit_post_tts_policy": "allow_interrupt"
    }
    
    # Simulate TTS processing
    input_data = {"text": "The weather today is sunny with a temperature of 75 degrees..."}
    
    print("1. Starting TTS for weather query...")
    result = await tts_state.process(input_data, context)
    print(f"   TTS Result: {result.status.value} - {result.message}")
    
    # Simulate interrupt during TTS
    print("2. Simulating user interrupt: 'What about tomorrow?'")
    interrupt_data = {
        "session_id": session_id,
        "user_input": "What about tomorrow?",
        "playback_position": 3.2,
        "audio_path": result.outputs.get("audio_path"),
        "timestamp": "2025-01-14T10:30:45Z"
    }
    
    # Trigger interrupt handling
    print("3. Triggering interrupt handling...")
    interrupt_handled = await state_manager.handle_interrupt_event(interrupt_data)
    print(f"   Interrupt handled: {interrupt_handled}")
    
    # Check interrupt context
    interrupt_context = await memory_manager.get_interrupt_context()
    print(f"4. Interrupt context: {interrupt_context}")
    
    print("✅ Reversible interrupt test completed")


async def test_irreversible_interrupt():
    """Test interrupt handling for irreversible actions (money transfer)."""
    print("\n=== Testing Irreversible Action Interrupt ===")
    
    # Setup
    session_id = "test_session_irreversible"
    memory_manager = MemoryManager(session_id, "test_user")
    agent_registry = AgentRegistry()
    agent_registry.register("tts_agent", MockTTSAgent())
    
    interrupt_config = InterruptConfig(
        detection=InterruptDetectionConfig(
            enabled=True,
            vad_threshold=0.01,
            confirmation_window_seconds=0.5
        )
    )
    
    state_manager = StateManager(
        session_id=session_id,
        user_id="test_user",
        memory_manager=memory_manager,
        agent_registry=agent_registry,
        interrupt_config=interrupt_config
    )
    
    # Add state_manager to agent_registry for TTSState access
    agent_registry.state_manager = state_manager
    
    # Create TTS State
    tts_state = TTSState(
        state_id="tts_state",
        agent_registry=agent_registry,
        session_id=session_id,
        interrupt_config=interrupt_config
    )
    
    # Test context for irreversible action (money transfer)
    context = {
        "explicit_reversibility": False,
        "explicit_side_effect": "high",
        "explicit_post_tts_policy": "no_interrupt"
    }
    
    # Simulate TTS processing
    input_data = {"text": "I'm processing your transfer of $500 to John Smith's account..."}
    
    print("1. Starting TTS for money transfer...")
    result = await tts_state.process(input_data, context)
    print(f"   TTS Result: {result.status.value} - {result.message}")
    
    # Simulate interrupt during TTS
    print("2. Simulating user interrupt: 'Wait, stop! I meant $50!'")
    interrupt_data = {
        "session_id": session_id,
        "user_input": "Wait, stop! I meant $50!",
        "playback_position": 4.1,
        "audio_path": result.outputs.get("audio_path"),
        "timestamp": "2025-01-14T10:35:22Z"
    }
    
    # Trigger interrupt handling
    print("3. Triggering interrupt handling...")
    interrupt_handled = await state_manager.handle_interrupt_event(interrupt_data)
    print(f"   Interrupt handled: {interrupt_handled}")
    
    # Check interrupt context
    interrupt_context = await memory_manager.get_interrupt_context()
    print(f"4. Interrupt context: {interrupt_context}")
    
    print("✅ Irreversible interrupt test completed")


async def test_interrupt_handler_state_directly():
    """Test InterruptHandlerState directly."""
    print("\n=== Testing InterruptHandlerState Directly ===")
    
    # Setup
    session_id = "test_session_direct"
    agent_registry = AgentRegistry()
    
    interrupt_config = InterruptConfig(
        detection=InterruptDetectionConfig(
            enabled=True,
            vad_threshold=0.01,
            confirmation_window_seconds=0.5
        )
    )
    
    # Create InterruptHandlerState
    interrupt_state = InterruptHandlerState(
        state_id="interrupt_handler",
        agent_registry=agent_registry,
        session_id=session_id,
        interrupt_config=interrupt_config
    )
    
    # Test reversible action
    print("1. Testing reversible action handling...")
    reversible_input = {
        "interrupt_detected": True,
        "user_input": "What about tomorrow?",
        "playback_position": 3.2,
        "audio_path": "/tmp/weather.mp3",
        "session_id": session_id
    }
    
    reversible_context = {
        "explicit_reversibility": True,
        "explicit_side_effect": "none",
        "explicit_post_tts_policy": "allow_interrupt"
    }
    
    result = await interrupt_state.process(reversible_input, reversible_context)
    print(f"   Reversible result: {result.outputs}")
    
    # Test irreversible action
    print("2. Testing irreversible action handling...")
    irreversible_input = {
        "interrupt_detected": True,
        "user_input": "Wait, stop!",
        "playback_position": 4.1,
        "audio_path": "/tmp/transfer.mp3",
        "session_id": session_id
    }
    
    irreversible_context = {
        "explicit_reversibility": False,
        "explicit_side_effect": "high",
        "explicit_post_tts_policy": "no_interrupt"
    }
    
    result = await interrupt_state.process(irreversible_input, irreversible_context)
    print(f"   Irreversible result: {result.outputs}")
    
    print("✅ InterruptHandlerState direct test completed")


async def main():
    """Run all interrupt flow tests."""
    print("🚀 Starting Interrupt Flow Integration Tests")
    
    try:
        await test_reversible_interrupt()
        await test_irreversible_interrupt()
        await test_interrupt_handler_state_directly()
        
        print("\n🎉 All interrupt flow tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
