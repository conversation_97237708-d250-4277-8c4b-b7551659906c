"""
Automated test for the TTS Interrupt Handling System (Docker/CI compatible)

This script tests:
1. TTS playback with interrupt detection (simulated VAD)
2. Simulated user interrupt mid-playback
3. Interrupt handler state processing
4. Acknowledgment message generation
5. Resume/handling logic

Run this script in Docker or CI to verify the core interrupt handling flow.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Always run in dummy VAD mode for this demo
DUMMY_VAD_MODE = True

from utils.audio_utils import TTSPlaybackController
from core.interruption.interrupt_handler_state import InterruptHandlerState
from core.interruption.action_reversibility import ActionReversibilityDetector
from core.config.interrupt_config import get_interrupt_config
from unittest.mock import Mock, AsyncMock

class InterruptHandlingTest:
    def __init__(self):
        self.session_id = "test_session"
        self.interrupt_config = get_interrupt_config()
        self.dummy_vad_mode = DUMMY_VAD_MODE

    async def run(self):
        print("\n=== TTS Interrupt Handling Automated Test ===\n")
        print("[INFO] Running in DUMMY VAD mode: all voice activity will be simulated as detected.")
        await self.test_interrupt_flow()
        print("\n=== All tests completed ===\n")

    async def test_interrupt_flow(self):
        print("[TEST] Simulate TTS playback and user interrupt...")
        # Step 1: Set up TTS playback controller
        controller = TTSPlaybackController(self.session_id, interrupt_config=self.interrupt_config)
        # Step 2: Simulate TTS playback (no real audio needed for this test)
        print("  - Starting TTS playback (simulated)...")
        # Simulate successful playback result
        class DummyResult:
            def __init__(self):
                self.status = type('Status', (), {'value': 'SUCCESS'})
                self.outputs = {"audio_path": "/tmp/fake_audio.wav", "audio_duration": 2.0}
        playback_result = DummyResult()
        print("    PASS: TTS playback started.")

        # Step 3: Simulate user interrupt
        print("  - Simulating user interrupt...")
        # We'll call the interrupt handler directly
        mock_registry = Mock()
        handler = InterruptHandlerState("interrupt_handler", mock_registry, self.session_id, interrupt_config=self.interrupt_config)
        handler._publish_notification = AsyncMock()
        interrupt_input = {
            "interrupt_detected": True,
            "user_input": "Wait, stop!",
            "playback_position": 1.5,
            "audio_path": "/tmp/fake_audio.wav",
            "session_id": self.session_id
        }
        context = {
            "explicit_reversibility": False,
            "explicit_side_effect": True,
            "explicit_post_tts_policy": "continue_and_explain"
        }
        result = await handler.process(interrupt_input, context)
        if result.status.value != "success":
            print("[DEBUG] Interrupt handler result status:", result.status)
            print("[DEBUG] Interrupt handler result message:", result.message)
            print("[DEBUG] Full result:", result)
        assert result.status.value == "success", f"Interrupt handler did not process successfully. Status: {result.status}, Message: {result.message}"
        print("    PASS: Interrupt handler processed.")
        ack_msg = result.outputs.get("acknowledgment_message", "")
        assert "already been completed" in ack_msg or "Allow me to finish" in ack_msg, "Acknowledgment message not as expected"
        print(f"    PASS: Acknowledgment message generated: '{ack_msg}'")
        should_resume = result.outputs.get("should_resume_tts", None)
        print(f"    INFO: Should resume TTS: {should_resume}")
        print("[TEST] Interrupt flow test completed.\n")

async def main():
    test = InterruptHandlingTest()
    await test.run()

if __name__ == "__main__":
    asyncio.run(main())
